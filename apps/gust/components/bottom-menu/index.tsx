'use client';

import Link from 'next/link';
import { useScopedI18n } from '@/lib/i18n/client-translator';
import { Url } from 'next/dist/shared/lib/router/router';
import { FC, ReactNode, useEffect, useState } from 'react';
import IconWrapper from '../icons';
import { cn } from '@/lib/utils';
import useWindowSizeQuery from '@/hooks/use-window-size-query';
import { MENU_BUTTONS, ROUTES } from '@/constants';
import styles from './styles.module.scss';
import useClientOnly from '@/hooks/use-client-only';
import { useRouter } from 'next/navigation';
import MoreDrawer from './more-drawer';
import { useUserStore } from '@/store/useUserStore';

interface BottomMenuItemProps {
  title: string;
  url: Url;
  icon: ReactNode;
  active?: boolean;
  isSearch?: boolean;
  key: string;
  onClick?: () => void;
}

interface BottomMenuProps {
  activeMenuBtn?: MENU_BUTTONS;
}

const menuListItems = [
  { key: MENU_BUTTONS.INBOX, icon: <IconWrapper name="MessagesIcon" />, url: ROUTES.INBOX, titleKey: 'inbox' },
  {
    key: MENU_BUTTONS.RESERVATION,
    icon: <IconWrapper name="CalendarIconBlue" />,
    url: ROUTES.MY_RESERVATIONS,
    titleKey: 'reservation',
  },
  { key: MENU_BUTTONS.SEARCH, icon: <IconWrapper name="RentoorIcon" />, url: ROUTES.HOME, titleKey: 'search' },
  { key: MENU_BUTTONS.FAVORITE, icon: <IconWrapper name="FavoriteIcon" />, url: ROUTES.WISHLIST, titleKey: 'favorite' },
  { key: MENU_BUTTONS.MORE, icon: <IconWrapper name="UserIcon" />, url: '#', titleKey: 'more' },
] as const;

const BottomMenuItem = ({ title, onClick, url, icon, isSearch = false, active = false }: BottomMenuItemProps) => {
  const router = useRouter();
  useEffect(() => {
    router.prefetch(url as string);
  }, []);

  return (
    <li className="flex-1">
      <button
        onClick={onClick}
        className={cn(
          `${styles.bottomMenuBtn}`,
          `inline-flex h-[65px] w-full flex-col items-center justify-center gap-[6px]`,
          active && !isSearch ? styles.active : ''
        )}>
        {icon}
        <span
          className={cn('text-blue-soft xxs:text-xs text-center text-[8px] font-normal', active ? 'text-primary' : '')}>
          {title}
        </span>
      </button>
    </li>
  );
};

const BottomMenuComp: FC<BottomMenuProps> = ({ activeMenuBtn = MENU_BUTTONS.SEARCH }) => {
  const t = useScopedI18n('bottomMenu');
  const [isMoreDrawerOpen, setIsMoreDrawerOpen] = useState(false);
  const isMobile = useWindowSizeQuery('mobile');
  const [scrollY, setScrollY] = useState<number>(0);
  const [showMenu, setShowMenu] = useState<boolean>(true);
  const router = useRouter();
  const { userData, setAuthModalOpen } = useUserStore((state) => state);

  useEffect(() => {
    const handleScroll = () => {
      if (isMobile) {
        const currentScrollY = window.scrollY;
        if (currentScrollY > scrollY && currentScrollY > 50) {
          setShowMenu(false);
        } else if (currentScrollY < scrollY) {
          setShowMenu(true);
        }
        setScrollY(currentScrollY);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [scrollY, isMobile]);

  const handleMenuItemClick = (link: string, itemKey: MENU_BUTTONS) => {
    if (itemKey === MENU_BUTTONS.MORE) {
      setIsMoreDrawerOpen(true);
    } else {
      if (userData && link) {
        router.push(link);
      } else {
        setAuthModalOpen(true);
      }
    }
  };
  return (
    <div
      className={cn(
        'block',
        'shadow-mobile bg-white-full rad fixed bottom-0 left-0 z-50 w-full rounded-t-2xl px-0.5',
        `transition-transform duration-300`,
        `${showMenu ? 'transform-none' : 'translate-y-full transform'}`
      )}>
      <ul className="flex items-center justify-center gap-[1px] px-2">


        {menuListItems.map((item) => (
          <BottomMenuItem
            active={activeMenuBtn === item.key}
            isSearch={item.key === MENU_BUTTONS.SEARCH}
            key={item.key}
            icon={item.icon}
            url={item.url}
            title={t(item.titleKey)}
            onClick={() => handleMenuItemClick(item.url, item.key)}
          />
        ))}


        <MoreDrawer open={isMoreDrawerOpen} setOpen={setIsMoreDrawerOpen} />
      </ul>
    </div>
  );
};

const BottomMenu: FC<BottomMenuProps> = (props) => {
  const isMobile = useWindowSizeQuery('mobile');
  const isClient = useClientOnly({ setStateValue: isMobile, dependencyArr: [isMobile] });

  return <>{isClient && <BottomMenuComp {...props} />}</>;
};

export default BottomMenu;
