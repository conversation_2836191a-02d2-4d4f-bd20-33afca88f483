{"name": "@rnt/gust", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap --config next-sitemap.config.js"}, "dependencies": {"@ably/chat": "^0.2.1", "@rnt/image-config": "*", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-direction": "^1.0.1", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@sentry/nextjs": "^9.22.0", "@splidejs/react-splide": "^0.7.12", "@tanstack/react-query": "^5.51.11", "@tanstack/react-query-devtools": "^5.51.11", "@vis.gl/react-google-maps": "^1.1.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "emoji-picker-react": "^4.12.0", "i": "^0.3.7", "iconsax-react": "^0.0.8", "input-otp": "^1.2.4", "intl-locale-textinfo-polyfill": "^2.1.1", "intl-tel-input": "^22.0.2", "js-cookie": "^3.0.5", "lg-thumbnail": "^1.2.1", "lg-zoom": "^1.3.0", "lightgallery": "^2.8.2", "lucide-react": "^0.373.0", "moment": "^2.30.1", "next": "14.2.3", "next-international": "^1.2.4", "npm": "^11.1.0", "rc-time-picker": "^3.7.3", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.0.7", "react-dom": "^18.2.0", "react-hook-form": "^7.51.3", "react-share": "^5.2.2", "sonner": "^1.4.41", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "vaul": "^0.9.1", "zod": "^3.23.4"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20.12.7", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "autoprefixer": "^10.4.19", "eslint-config-rnt": "*", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "tsconfig": "*", "typescript": "^5.4.5"}}