// Crypto polyfill for environments that don't support crypto.randomUUID()
// This should be imported early in the application lifecycle

// No need to declare randomUUID as it's already in TypeScript's built-in definitions

// UUID v4 generator function
function generateUUIDv4(): `${string}-${string}-${string}-${string}-${string}` {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  }) as `${string}-${string}-${string}-${string}-${string}`;
}

// Polyfill for different environments
try {
  // Browser environment
  if (typeof window !== 'undefined') {
    if (!window.crypto) {
      (window as any).crypto = {};
    }
    if (!window.crypto.randomUUID) {
      window.crypto.randomUUID = generateUUIDv4;
    }
  }

  // Global environment (works for both browser and Node.js)
  if (typeof globalThis !== 'undefined') {
    if (!globalThis.crypto) {
      (globalThis as any).crypto = {};
    }
    if (!globalThis.crypto.randomUUID) {
      globalThis.crypto.randomUUID = generateUUIDv4;
    }
  }

  // Node.js environment
  if (typeof global !== 'undefined') {
    if (!global.crypto) {
      (global as any).crypto = {};
    }
    if (!global.crypto.randomUUID) {
      global.crypto.randomUUID = generateUUIDv4;
    }
  }

  // Self environment (for Web Workers)
  if (typeof self !== 'undefined' && typeof (self as any).crypto !== 'undefined') {
    if (!(self as any).crypto.randomUUID) {
      (self as any).crypto.randomUUID = generateUUIDv4;
    }
  }
} catch (error) {
  // Silently fail if we can't add the polyfill
  console.warn('Could not add crypto.randomUUID polyfill:', error);
}

export {};
