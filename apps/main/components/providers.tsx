'use client';
import type { FC, PropsWithChildren, ReactElement } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { DirectionProvider } from '@radix-ui/react-direction';
import getQueryClient from '@/lib/react-query/get-query-client';
import { I18nProviderClient } from '@/lib/i18n/client-translator';
import { type Direction } from '@/types/common';
import { Toaster } from '@/components/ui/sonner';
import DataContextProvider from '@/contexts/data-context';
import LoginModal from './header/login-modal';
import AuthWrapper from '@/contexts/auth-context';

type ProvidersProps = {
  locale: string;
  dir: Direction;
} & PropsWithChildren;

const Providers: FC<ProvidersProps> = ({ locale, dir, children }): ReactElement => {
  const queryClient = getQueryClient();

  return (
    <QueryClientProvider client={queryClient}>
      <I18nProviderClient locale={locale}>
        <DirectionProvider dir={dir}>
          <DataContextProvider>
            <AuthWrapper>
              {children}
              <LoginModal />
            </AuthWrapper>
            <Toaster />
          </DataContextProvider>
        </DirectionProvider>
        <ReactQueryDevtools initialIsOpen={false} />
      </I18nProviderClient>
    </QueryClientProvider>
  );
};
export default Providers;
